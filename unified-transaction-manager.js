/**
 * وحدة إدارة المعاملات الموحدة المحسنة
 * تقوم هذه الوحدة بتوحيد جميع عمليات المعاملات في النظام (شراء، بيع، استلام، صرف)
 * وتضمن تحديث المخزون والخزينة بشكل متسق باستخدام معاملات قاعدة البيانات
 */

const { logError, logSystem } = require('./error-handler');
const { validateTransaction, validateItem, validateCustomer } = require('./data-validator');
const DatabaseManager = require('./database-singleton');
const eventSystem = require('./event-system');

// مرجع لقاعدة البيانات (سيتم الحصول عليه من مدير قاعدة البيانات)
let db = null;

/**
 * إعادة حساب إجمالي الأرباح من جميع المعاملات
 * @returns {number} - إجمالي الأرباح المحسوب
 */
function recalculateTotalProfits() {
  try {
    console.log('[PROFIT-RECALC] بدء إعادة حساب إجمالي الأرباح...');
    console.log('[PROFIT-RECALC] حساب الأرباح من معاملات البيع فقط (تكاليف النقل مخصومة بالفعل من الربح في البيع)');

    // حساب إجمالي الأرباح من معاملات البيع فقط
    // الربح في معاملات البيع يتضمن بالفعل خصم تكاليف النقل
    const totalProfitStmt = db.prepare(`
      SELECT COALESCE(SUM(profit), 0) as total_profit
      FROM transactions
      WHERE transaction_type = 'sale'
    `);

    const result = totalProfitStmt.get();
    const totalProfit = Number(result ? result.total_profit : 0);

    console.log(`[PROFIT-RECALC] إجمالي الأرباح المحسوب من معاملات البيع: ${totalProfit}`);
    console.log(`[PROFIT-RECALC] ملاحظة: تكاليف النقل في المشتريات لا تؤثر على الأرباح، فقط على الرصيد الحالي`);

    return totalProfit;
  } catch (error) {
    console.error('[PROFIT-RECALC] خطأ في إعادة حساب الأرباح:', error);
    return 0;
  }
}

/**
 * تحديث إجمالي الأرباح في قاعدة البيانات مع آلية محسنة
 * @param {number} totalProfit - إجمالي الأرباح المحسوب
 * @returns {boolean} - نجح التحديث أم لا
 */
function updateProfitTotalInDatabase(totalProfit) {
  try {
    console.log(`[PROFIT-SAVE] بدء تحديث profit_total إلى: ${totalProfit}`);

    // استخدام معاملة قاعدة البيانات لضمان الاتساق
    const updateTransaction = db.transaction(() => {
      // تحديث القيمة
      const updateStmt = db.prepare(`
        UPDATE cashbox
        SET profit_total = ?,
            updated_at = ?
        WHERE id = 1
      `);

      const updateResult = updateStmt.run(totalProfit, new Date().toISOString());

      if (updateResult.changes === 0) {
        throw new Error('لم يتم تحديث أي صف في جدول cashbox');
      }

      console.log(`[PROFIT-SAVE] تم تحديث profit_total. الصفوف المتأثرة: ${updateResult.changes}`);

      // التحقق الفوري من حفظ القيمة
      const verifyStmt = db.prepare('SELECT profit_total FROM cashbox WHERE id = 1');
      const verifyResult = verifyStmt.get();

      if (!verifyResult) {
        throw new Error('فشل في استرجاع البيانات للتحقق');
      }

      const savedValue = Number(verifyResult.profit_total);

      if (Math.abs(savedValue - totalProfit) > 0.01) {
        throw new Error(`فشل في حفظ القيمة: متوقع ${totalProfit} لكن تم حفظ ${savedValue}`);
      }

      console.log(`[PROFIT-SAVE] تم حفظ القيمة بنجاح: ${savedValue}`);

      return {
        success: true,
        savedValue: savedValue,
        changes: updateResult.changes
      };
    });

    const result = updateTransaction();
    return result.success;

  } catch (error) {
    console.error('[PROFIT-SAVE] خطأ في تحديث profit_total:', error);
    return false;
  }
}

/**
 * تهيئة وحدة إدارة المعاملات الموحدة
 */
function initialize() {
  try {
    console.log('جاري تهيئة وحدة إدارة المعاملات الموحدة...');

    // الحصول على اتصال قاعدة البيانات من مدير قاعدة البيانات
    const dbManager = DatabaseManager.getInstance();
    db = dbManager.getConnection();

    if (!db) {
      throw new Error('فشل في الحصول على اتصال قاعدة البيانات');
    }

    console.log('تم تهيئة وحدة إدارة المعاملات الموحدة بنجاح');
    logSystem('تم تهيئة وحدة إدارة المعاملات الموحدة بنجاح', 'info');

    return true;
  } catch (error) {
    console.error('خطأ في تهيئة وحدة إدارة المعاملات الموحدة:', error);
    logError(error, 'initialize - unified-transaction-manager');
    return false;
  }
}

/**
 * إنشاء معاملة جديدة (شراء، بيع، استلام، صرف)
 * @param {Object} transaction - بيانات المعاملة
 * @returns {Promise<Object>} - نتيجة إنشاء المعاملة
 */
function createTransaction(transaction) {
  console.log('[TRANSACTION-DEBUG] بدء إنشاء معاملة جديدة:', transaction);

  // تسجيل المعاملة كاملة للتشخيص
  logSystem(`[TRANSACTION-DEBUG] بيانات المعاملة الكاملة في createTransaction: ${JSON.stringify(transaction)}`, 'info');

  // تسجيل معلومات إضافية للتشخيص
  console.log(`[TRANSACTION-DEBUG] معلومات تشخيصية للمعاملة:`);
  console.log(`- نوع المعاملة: ${transaction.transaction_type}`);
  console.log(`- معرف الصنف: ${transaction.item_id} (النوع: ${typeof transaction.item_id})`);
  console.log(`- الكمية: ${transaction.quantity} (النوع: ${typeof transaction.quantity})`);
  console.log(`- السعر: ${transaction.price} (النوع: ${typeof transaction.price})`);
  console.log(`- سعر البيع: ${transaction.selling_price} (النوع: ${typeof transaction.selling_price})`);

  // التحقق من وجود الحد الأدنى
  if (transaction.minimum_quantity !== undefined) {
    logSystem(`[TRANSACTION-DEBUG] الحد الأدنى في المعاملة: ${transaction.minimum_quantity}`, 'info');
  } else {
    logSystem(`[TRANSACTION-DEBUG] الحد الأدنى غير موجود في المعاملة`, 'info');
  }

  return new Promise((resolve, reject) => {
    try {
      // التحقق من صحة بيانات المعاملة
      if (!validateTransaction(transaction)) {
        console.error('بيانات المعاملة غير صالحة');
        reject(new Error('بيانات المعاملة غير صالحة'));
        return;
      }

      // التحقق من وجود قاعدة البيانات
      if (!db) {
        console.error('قاعدة البيانات غير متصلة');
        reject(new Error('قاعدة البيانات غير متصلة'));
        return;
      }

      // استخراج البيانات من المعاملة
      const {
        item_id,
        transaction_type,
        quantity,
        price,
        selling_price,
        transport_cost,
        customer_id,
        invoice_number,
        notes,
        user_id,
        transaction_date
      } = transaction;

      // التحقق من وجود الصنف
      const itemStmt = db.prepare('SELECT * FROM items WHERE id = ?');
      const item = itemStmt.get(item_id);

      if (!item) {
        reject(new Error(`الصنف غير موجود: ${item_id}`));
        return;
      }

      // التحقق من وجود العميل إذا كانت معاملة بيع
      if (transaction_type === 'sale' && customer_id) {
        const customerStmt = db.prepare('SELECT * FROM customers WHERE id = ?');
        const customer = customerStmt.get(customer_id);

        if (!customer) {
          reject(new Error(`العميل غير موجود: ${customer_id}`));
          return;
        }
      }

      // التحقق من الكمية المتوفرة إذا كانت معاملة بيع أو صرف
      if (transaction_type === 'sale' || transaction_type === 'withdrawal') {
        const inventoryStmt = db.prepare('SELECT current_quantity FROM inventory WHERE item_id = ?');
        const inventory = inventoryStmt.get(item_id);

        if (!inventory) {
          reject(new Error(`الصنف غير موجود في المخزون: ${item.name}`));
          return;
        }

        if (inventory.current_quantity < quantity) {
          reject(new Error(`الكمية المتوفرة غير كافية للصنف ${item.name}. المتوفر: ${inventory.current_quantity}, المطلوب: ${quantity}`));
          return;
        }
      }

      // التحقق من الكمية المسترجعة إذا كانت معاملة استرجاع
      if (transaction_type === 'return' && customer_id) {
        logSystem(`التحقق من الكمية المسترجعة للصنف ${item_id} للعميل ${customer_id}`, 'info');

        // الحصول على إجمالي الكمية المباعة للعميل لهذا الصنف
        const soldQuantityStmt = db.prepare(`
          SELECT SUM(quantity) as total_sold
          FROM transactions
          WHERE item_id = ? AND customer_id = ? AND transaction_type = 'sale'
        `);
        const soldResult = soldQuantityStmt.get(item_id, customer_id);
        const totalSold = soldResult ? soldResult.total_sold || 0 : 0;

        // الحصول على إجمالي الكمية المسترجعة سابقاً للعميل لهذا الصنف
        const returnedQuantityStmt = db.prepare(`
          SELECT SUM(quantity) as total_returned
          FROM transactions
          WHERE item_id = ? AND customer_id = ? AND transaction_type = 'return'
        `);
        const returnedResult = returnedQuantityStmt.get(item_id, customer_id);
        const totalReturned = returnedResult ? returnedResult.total_returned || 0 : 0;

        // حساب الكمية المتاحة للاسترجاع
        const availableForReturn = totalSold - totalReturned;

        logSystem(`إجمالي الكمية المباعة: ${totalSold}, إجمالي الكمية المسترجعة سابقاً: ${totalReturned}, المتاح للاسترجاع: ${availableForReturn}`, 'info');

        if (quantity > availableForReturn) {
          reject(new Error(`الكمية المطلوب استرجاعها (${quantity}) تتجاوز الكمية المتاحة للاسترجاع (${availableForReturn}) للعميل ${customer_id}`));
          return;
        }
      }

      // إنشاء معرف فريد للمعاملة
      const transactionId = generateTransactionId(transaction_type);
      console.log('تم إنشاء معرف فريد للمعاملة:', transactionId);

      // استيراد وظائف حساب الربح
      const { calculateProfit, calculateProfitWithTransport } = require('./utils/profitCalculator');

      // حساب السعر الإجمالي
      const totalPrice = quantity * price;
      let profit = 0;

      if (transaction_type === 'sale' && selling_price > 0) {
        // حساب الربح في حالة البيع (بدون تأثير مصاريف النقل)
        const inventoryStmt = db.prepare('SELECT avg_price FROM inventory WHERE item_id = ?');
        const inventory = inventoryStmt.get(item_id);
        const avgPrice = inventory ? inventory.avg_price : 0;

        // حساب الربح الأساسي: (سعر البيع - متوسط سعر الشراء) × الكمية
        // ملاحظة: مصاريف النقل لا تؤثر على حساب الأرباح
        // مصاريف النقل تؤثر فقط على الرصيد الحالي في عمليات الشراء
        profit = (selling_price - avgPrice) * quantity;

        // التأكد من أن الربح لا يكون سالباً
        profit = Math.max(0, profit);

        // تسجيل معلومات حساب الربح للتشخيص
        logSystem(`حساب الربح بدون تأثير مصاريف النقل: (سعر البيع ${selling_price} - متوسط سعر الشراء ${avgPrice}) × الكمية ${quantity} = ${profit}`, 'info');
        logSystem(`ملاحظة: مصاريف النقل لا تؤثر على حساب الأرباح، فقط على الرصيد الحالي في عمليات الشراء`, 'info');
      }

      // بدء معاملة قاعدة البيانات
      try {
        console.log('بدء معاملة قاعدة البيانات...');

        const dbResult = db.transaction(() => {
          console.log('داخل معاملة قاعدة البيانات...');

          // إضافة المعاملة
          const insertStmt = db.prepare(`
            INSERT INTO transactions (
              transaction_id, item_id, transaction_type, quantity, price,
              selling_price, total_price, profit, transport_cost, customer_id, invoice_number,
              notes, user_id, transaction_date, skip_inventory_update, customer
            )
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
          `);

          const now = new Date().toISOString();
          console.log('جاري تنفيذ استعلام إضافة المعاملة...');

          // تسجيل الحد الأدنى للتشخيص
          const minimumQuantity = transaction.minimum_quantity !== undefined ? Number(transaction.minimum_quantity) : 0;
          logSystem(`الحد الأدنى قبل إضافة المعاملة: ${minimumQuantity}`, 'info');

          // الحصول على اسم العميل إذا كان معرف العميل موجودًا
          let customerName = '';
          if (customer_id) {
            try {
              const customerStmt = db.prepare('SELECT name FROM customers WHERE id = ?');
              const customer = customerStmt.get(customer_id);
              if (customer) {
                customerName = customer.name;
              }
            } catch (error) {
              console.error('خطأ في الحصول على اسم العميل:', error);
              logError(error, 'createTransaction - get customer name');
            }
          }

          // معالجة رقم الفاتورة بشكل آمن
          // نحتفظ برقم الفاتورة كما هو دون أي تحويل أو معالجة
          const safeInvoiceNumber = invoice_number !== undefined && invoice_number !== null ? invoice_number : '';

          const result = insertStmt.run(
            transactionId,
            item_id,
            transaction_type,
            quantity,
            price,
            selling_price || 0,
            totalPrice,
            profit,
            transport_cost || 0,
            customer_id || null,
            safeInvoiceNumber,
            notes || '',
            user_id || null,
            transaction_date || now,
            transaction.skip_inventory_update || 0,
            customerName || ''
          );

          console.log('تم تنفيذ استعلام إضافة المعاملة بنجاح:', result);
          const transactionId_db = result.lastInsertRowid;

        // تحديث المخزون
        if (!transaction.skip_inventory_update) {
          // تسجيل معلومات المعاملة قبل تحديث المخزون
          logSystem(`معلومات المعاملة قبل تحديث المخزون:`, 'info');
          logSystem(`- معرف الصنف: ${item_id}`, 'info');
          logSystem(`- نوع المعاملة: ${transaction_type}`, 'info');
          logSystem(`- الكمية: ${quantity}`, 'info');
          logSystem(`- السعر: ${price}`, 'info');
          logSystem(`- سعر البيع: ${selling_price}`, 'info');
          logSystem(`- الحد الأدنى: ${transaction.minimum_quantity}`, 'info');

          // تسجيل معلومات إضافية لعمليات الإرجاع
          if (transaction_type === 'return') {
            console.log(`[RETURN-FIX] معالجة عملية إرجاع في createTransaction للصنف ${item_id} بكمية ${quantity}`);
            logSystem(`[RETURN-FIX] معالجة عملية إرجاع في createTransaction`, 'info');
            logSystem(`[RETURN-FIX] - معرف الصنف: ${item_id}`, 'info');
            logSystem(`[RETURN-FIX] - معرف العميل: ${customer_id}`, 'info');
            logSystem(`[RETURN-FIX] - رقم الفاتورة: ${invoice_number}`, 'info');

            // الحصول على معلومات المخزون قبل التحديث
            try {
              const inventoryStmt = db.prepare('SELECT * FROM inventory WHERE item_id = ?');
              const inventoryBefore = inventoryStmt.get(item_id);

              if (inventoryBefore) {
                console.log(`[RETURN-FIX] المخزون قبل التحديث للصنف ${item_id}: ${inventoryBefore.current_quantity}`);
                logSystem(`[RETURN-FIX] المخزون قبل التحديث للصنف ${item_id}: ${inventoryBefore.current_quantity}`, 'info');
              } else {
                console.log(`[RETURN-FIX] لم يتم العثور على سجل مخزون للصنف ${item_id}`);
                logSystem(`[RETURN-FIX] لم يتم العثور على سجل مخزون للصنف ${item_id}`, 'warning');
              }
            } catch (inventoryError) {
              console.error(`[RETURN-FIX] خطأ في الحصول على معلومات المخزون:`, inventoryError);
              logSystem(`[RETURN-FIX] خطأ في الحصول على معلومات المخزون: ${inventoryError.message}`, 'error');
            }
          }

          console.log(`[RETURN-FIX] استدعاء updateInventoryAfterTransaction للصنف ${item_id} بنوع ${transaction_type} وكمية ${quantity}`);
          const inventoryUpdateResult = updateInventoryAfterTransaction(
            item_id,
            transaction_type,
            quantity,
            price,
            selling_price,
            transaction
          );

          // التحقق من نجاح تحديث المخزون
          if (!inventoryUpdateResult) {
            const errorMsg = `فشل في تحديث المخزون للمعاملة ${transaction_type} للصنف ${item_id}`;
            console.error(`[RETURN-FIX] ${errorMsg}`);
            logError(new Error(errorMsg), 'createTransaction');
            throw new Error(errorMsg);
          }

          console.log(`[RETURN-FIX] تم تحديث المخزون بنجاح للمعاملة ${transaction_type} للصنف ${item_id}`);
          logSystem(`تم تحديث المخزون بنجاح للمعاملة ${transaction_type} للصنف ${item_id}`, 'info');

          // التحقق من المخزون بعد التحديث
          if (transaction_type === 'return') {
            try {
              const inventoryStmt = db.prepare('SELECT * FROM inventory WHERE item_id = ?');
              const inventoryAfter = inventoryStmt.get(item_id);

              if (inventoryAfter) {
                console.log(`[RETURN-FIX] المخزون بعد التحديث للصنف ${item_id}: ${inventoryAfter.current_quantity}`);
                logSystem(`[RETURN-FIX] المخزون بعد التحديث للصنف ${item_id}: ${inventoryAfter.current_quantity}`, 'info');
              } else {
                console.log(`[RETURN-FIX] لم يتم العثور على سجل مخزون للصنف ${item_id} بعد التحديث`);
                logSystem(`[RETURN-FIX] لم يتم العثور على سجل مخزون للصنف ${item_id} بعد التحديث`, 'warning');
              }
            } catch (inventoryError) {
              console.error(`[RETURN-FIX] خطأ في الحصول على معلومات المخزون بعد التحديث:`, inventoryError);
              logSystem(`[RETURN-FIX] خطأ في الحصول على معلومات المخزون بعد التحديث: ${inventoryError.message}`, 'error');
            }
          }
        } else {
          console.log(`[RETURN-FIX] تم تخطي تحديث المخزون للمعاملة ${transaction_type} للصنف ${item_id} (skip_inventory_update=true)`);
          logSystem(`تم تخطي تحديث المخزون للمعاملة ${transaction_type} للصنف ${item_id} (skip_inventory_update=true)`, 'info');
        }

        // تحديث سجل مبيعات العميل إذا كانت معاملة بيع وتحتوي على معرف العميل
        if (transaction_type === 'sale' && customer_id) {
          // ملاحظة: تم تعطيل إضافة سجل مبيعات العميل لأن الجدول غير موجود
          console.log('تم تخطي إضافة سجل مبيعات العميل لأن الجدول غير موجود');

          // تحديث رصيد العميل
          const updateCustomerStmt = db.prepare(`
            UPDATE customers
            SET balance = balance + ?
            WHERE id = ?
          `);

          updateCustomerStmt.run(
            totalPrice,
            customer_id
          );
        }

        // تحديث الخزينة
        console.log(`[TRANSACTION-DEBUG] بدء تحديث الخزينة بعد المعاملة...`);

        // إضافة تسجيلات إضافية قبل استدعاء updateCashboxAfterTransaction
        console.log(`[TRANSACTION-DEBUG] بيانات المعاملة قبل تحديث الخزينة:`, {
          transaction_type,
          totalPrice,
          profit,
          transport_cost,
          user_id
        });

        // استدعاء دالة تحديث الخزينة
        let cashboxUpdateResult;
        try {
          console.log(`[TRANSACTION-DEBUG] استدعاء updateCashboxAfterTransaction - النوع: ${transaction_type}, المبلغ: ${totalPrice}, الربح: ${profit}, مصاريف النقل: ${transport_cost || 0}, المستخدم: ${user_id}`);

          cashboxUpdateResult = updateCashboxAfterTransaction(
            transaction_type,
            totalPrice,
            profit,
            user_id,
            transport_cost
          );

          console.log(`[TRANSACTION-DEBUG] تم تحديث الخزينة بنجاح:`, cashboxUpdateResult);
        } catch (cashboxUpdateError) {
          console.error(`[TRANSACTION-DEBUG] خطأ في تحديث الخزينة:`, cashboxUpdateError);
          logError(cashboxUpdateError, 'createTransaction - updateCashboxAfterTransaction');
          throw cashboxUpdateError; // إعادة رمي الخطأ للتعامل معه في الدالة الأم
        }

        // الحصول على المعاملة المضافة
        const getTransactionStmt = db.prepare('SELECT * FROM transactions WHERE id = ?');
        const newTransaction = getTransactionStmt.get(transactionId_db);

        if (!newTransaction) {
          console.error('لم يتم العثور على المعاملة بعد إضافتها:', transactionId_db);
          throw new Error('فشل في إضافة المعاملة: لم يتم العثور على المعاملة بعد إضافتها');
        }

        console.log('تم الحصول على المعاملة المضافة بنجاح:', newTransaction);

        const transactionResult = {
          success: true,
          transaction: {
            ...newTransaction,
            _id: newTransaction.id.toString(),
            id: newTransaction.id.toString(),
            item_id: newTransaction.item_id.toString(),
            customer_id: newTransaction.customer_id ? newTransaction.customer_id.toString() : null,
            user_id: newTransaction.user_id ? newTransaction.user_id.toString() : null
          }
        };

        console.log('تم إنشاء المعاملة بنجاح، النتيجة:', transactionResult);

        // إرسال إشعار بإضافة معاملة جديدة
        eventSystem.notifyTransactionAdded({
          id: newTransaction.id,
          transaction_id: newTransaction.transaction_id,
          item_id: newTransaction.item_id,
          item_name: newTransaction.item_name || item.name,
          transaction_type: newTransaction.transaction_type,
          quantity: newTransaction.quantity,
          price: newTransaction.price,
          selling_price: newTransaction.selling_price,
          total_price: newTransaction.total_price,
          profit: newTransaction.profit,
          customer_id: newTransaction.customer_id,
          customer: newTransaction.customer,
          transaction_date: newTransaction.transaction_date,
          success: true
        });

        // الحصول على الخزينة المحدثة قبل إرسال الإشعار
        const getCashboxForNotifyStmt = db.prepare('SELECT * FROM cashbox LIMIT 1');
        const updatedCashbox = getCashboxForNotifyStmt.get();

        // التحقق من أن updatedCashbox تم استرجاعه بنجاح
        if (!updatedCashbox) {
          console.error('[ERROR] فشل في استرجاع بيانات الخزينة المحدثة. تأكد من وجود خزينة في قاعدة البيانات.');
          logSystem('[ERROR] فشل في استرجاع بيانات الخزينة المحدثة. تأكد من وجود خزينة في قاعدة البيانات.', 'error');
        }

        // تحديد قيم المعاملة
        const notifyTransactionType = transaction_type; // استخدام نوع المعاملة الفعلي
        const notifyAmount = Number(totalPrice) || 0;
        const notifyProfit = Number(profit) || 0;

        // إضافة تسجيلات إضافية قبل استدعاء notifyCashboxUpdated
        console.log(`[DEBUG] القيم المرسلة إلى notifyCashboxUpdated:`, {
          id: updatedCashbox ? updatedCashbox.id : 1,
          current_balance: updatedCashbox ? updatedCashbox.current_balance : 0,
          sales_total: updatedCashbox ? updatedCashbox.sales_total : 0,
          purchases_total: updatedCashbox ? updatedCashbox.purchases_total : 0,
          returns_total: updatedCashbox ? updatedCashbox.returns_total : 0,
          profit_total: updatedCashbox ? updatedCashbox.profit_total : 0,
          transaction_type: notifyTransactionType,
          amount: notifyAmount,
          profit: notifyProfit,
          success: true
        });

        // إرسال إشعار بتحديث الخزينة بعد عملية البيع
        eventSystem.notifyCashboxUpdated({
          id: updatedCashbox ? updatedCashbox.id : 1,
          current_balance: updatedCashbox ? updatedCashbox.current_balance : 0,
          sales_total: updatedCashbox ? updatedCashbox.sales_total : 0,
          purchases_total: updatedCashbox ? updatedCashbox.purchases_total : 0,
          returns_total: updatedCashbox ? updatedCashbox.returns_total : 0,
          transport_total: updatedCashbox ? updatedCashbox.transport_total : 0,
          profit_total: updatedCashbox ? updatedCashbox.profit_total : 0,
          transaction_type: notifyTransactionType,
          amount: notifyAmount,
          profit: notifyProfit,
          success: true
        });

        return transactionResult;
      })();

      console.log('تم تنفيذ معاملة قاعدة البيانات بنجاح، النتيجة:', dbResult);
      resolve(dbResult);
      return;
      } catch (transactionError) {
        console.error('خطأ في معاملة قاعدة البيانات:', transactionError);
        logError(transactionError, 'createTransaction - database transaction');
        reject(transactionError);
        return;
      }
    } catch (error) {
      console.error('خطأ في إنشاء المعاملة:', error);
      logError(error, 'createTransaction');
      reject(error);
    }
  });
}

/**
 * تحديث المخزون بعد المعاملة
 * @param {number} itemId - معرف الصنف
 * @param {string} transactionType - نوع المعاملة
 * @param {number} quantity - الكمية
 * @param {number} price - السعر
 * @param {number} sellingPrice - سعر البيع
 * @param {Object} transaction - المعاملة الكاملة (اختياري)
 * @returns {boolean} - نجاح العملية
 */
function updateInventoryAfterTransaction(itemId, transactionType, quantity, price, sellingPrice, transaction = null) {
  try {
    // التحقق من وجود قاعدة البيانات
    if (!db) {
      logError(new Error('قاعدة البيانات غير متصلة'), 'updateInventoryAfterTransaction');
      return false;
    }

    // التحويل إلى أرقام
    const numericItemId = Number(itemId);
    const numericQuantity = Number(quantity);
    const numericPrice = Number(price);
    const numericSellingPrice = Number(sellingPrice);

    // التحقق من صحة البيانات
    if (isNaN(numericItemId) || numericItemId <= 0) {
      logError(new Error(`معرف الصنف غير صالح: ${itemId}`), 'updateInventoryAfterTransaction');
      return false;
    }

    if (isNaN(numericQuantity) || numericQuantity <= 0) {
      logError(new Error(`الكمية غير صالحة: ${quantity}`), 'updateInventoryAfterTransaction');
      return false;
    }

    // الحصول على معلومات المخزون الحالية
    const getInventoryStmt = db.prepare('SELECT * FROM inventory WHERE item_id = ?');
    const inventory = getInventoryStmt.get(numericItemId);

    // تاريخ التحديث
    const now = new Date().toISOString();

    // معالجة المعاملة حسب نوعها
    if (transactionType === 'purchase' || transactionType === 'receiving') {
      // عملية شراء أو استلام
      // استخدام وحدة إدارة المخزون المحسنة
      const inventoryManager = require('./inventory-manager');

      // استخراج الحد الأدنى من المعاملة إذا كان موجودًا
      let minimumQuantity = null;
      try {
        if (transaction && transaction.minimum_quantity !== undefined) {
          // تحويل القيمة إلى رقم بشكل صحيح
          let processedMinQuantity = transaction.minimum_quantity;
          if (typeof processedMinQuantity === 'string') {
            processedMinQuantity = processedMinQuantity.trim();
          }

          minimumQuantity = Number(processedMinQuantity);

          // التأكد من أن القيمة صالحة
          if (isNaN(minimumQuantity)) {
            logSystem(`قيمة الحد الأدنى غير صالحة: ${transaction.minimum_quantity}، سيتم استخدام 0 بدلاً منها`, 'warning');
            minimumQuantity = 0;
          }

          // تسجيل القيمة النهائية للتشخيص
          logSystem(`تم تحويل الحد الأدنى من ${transaction.minimum_quantity} (${typeof transaction.minimum_quantity}) إلى ${minimumQuantity} (${typeof minimumQuantity})`, 'info');
        } else {
          logSystem(`لم يتم تمرير قيمة للحد الأدنى في المعاملة`, 'info');
        }
      } catch (error) {
        logSystem(`خطأ في معالجة الحد الأدنى: ${error.message}`, 'error');
        minimumQuantity = 0;
      }

      // تأكيد أن الحد الأدنى ليس null أو undefined
      if (minimumQuantity === null || minimumQuantity === undefined) {
        logSystem(`الحد الأدنى لا يزال null أو undefined، سيتم استخدام 0 بدلاً منه`, 'warning');
        minimumQuantity = 0;
      }

      // تسجيل معلومات المعاملة للتشخيص
      logSystem(`معلومات المعاملة:`, 'info');
      if (transaction) {
        logSystem(`- نوع المعاملة: ${transaction.transaction_type}`, 'info');
        logSystem(`- معرف الصنف: ${transaction.item_id}`, 'info');
        logSystem(`- الكمية: ${transaction.quantity}`, 'info');
        logSystem(`- السعر: ${transaction.price}`, 'info');
        logSystem(`- سعر البيع: ${transaction.selling_price}`, 'info');
        logSystem(`- الحد الأدنى (قبل المعالجة): ${transaction.minimum_quantity} (${typeof transaction.minimum_quantity})`, 'info');
        logSystem(`- الحد الأدنى (بعد المعالجة): ${minimumQuantity} (${typeof minimumQuantity})`, 'info');
      } else {
        logSystem(`- المعاملة غير متوفرة`, 'info');
      }

      logSystem(`استدعاء updateInventoryAfterPurchase مع الحد الأدنى: ${minimumQuantity}`, 'info');

      // استدعاء وظيفة تحديث المخزون بعد الشراء
      const result = inventoryManager.updateInventoryAfterPurchase(
        numericItemId,
        numericQuantity,
        numericPrice,
        numericSellingPrice,
        minimumQuantity
      );

      if (!result.success) {
        logError(new Error(`فشل في تحديث المخزون: ${result.error}`), 'updateInventoryAfterTransaction');
        return false;
      }

      return true;
    } else if (transactionType === 'sale' || transactionType === 'withdrawal') {
      // عملية بيع أو صرف
      if (!inventory) {
        logError(new Error(`الصنف غير موجود في المخزون: ${numericItemId}`), 'updateInventoryAfterTransaction');
        return false;
      }

      // التحقق من الكمية المتوفرة
      if (inventory.current_quantity < numericQuantity) {
        logError(new Error(`الكمية المتوفرة غير كافية. المتوفر: ${inventory.current_quantity}, المطلوب: ${numericQuantity}`), 'updateInventoryAfterTransaction');
        return false;
      }

      // استدعاء وظيفة تحديث المخزون بعد البيع من مدير المخزون
      const inventoryManager = require('./inventory-manager');
      logSystem(`استدعاء updateInventoryAfterSale للصنف ${numericItemId} بكمية ${numericQuantity} وسعر بيع ${numericSellingPrice}`, 'info');

      const result = inventoryManager.updateInventoryAfterSale(
        numericItemId,
        numericQuantity,
        numericSellingPrice
      );

      if (!result.success) {
        logError(new Error(`فشل في تحديث المخزون بعد البيع: ${result.error}`), 'updateInventoryAfterTransaction');
        return false;
      }

      return true;
    } else if (transactionType === 'return') {
      // عملية استرجاع (إضافة الكمية المسترجعة إلى المخزون)
      if (!inventory) {
        logError(new Error(`الصنف غير موجود في المخزون: ${numericItemId}`), 'updateInventoryAfterTransaction');
        return false;
      }

      logSystem(`[RETURN-DEBUG] معالجة عملية استرجاع للصنف ${numericItemId} بكمية ${numericQuantity}`, 'info');
      logSystem(`[RETURN-DEBUG] الكمية الحالية في المخزون قبل الإرجاع: ${inventory.current_quantity}`, 'info');
      console.log(`[RETURN-DEBUG] معالجة عملية استرجاع للصنف ${numericItemId} بكمية ${numericQuantity}`);
      console.log(`[RETURN-DEBUG] الكمية الحالية في المخزون قبل الإرجاع: ${inventory.current_quantity}`);

      // استخدام وظيفة تحديث المخزون بعد الإرجاع من مدير المخزون
      try {
        const inventoryManager = require('./inventory-manager');

        // التحقق من وجود وظيفة updateInventoryAfterReturn
        if (typeof inventoryManager.updateInventoryAfterReturn === 'function') {
          logSystem(`[RETURN-DEBUG] استدعاء updateInventoryAfterReturn للصنف ${numericItemId} بكمية ${numericQuantity}`, 'info');
          console.log(`[RETURN-DEBUG] استدعاء updateInventoryAfterReturn للصنف ${numericItemId} بكمية ${numericQuantity}`);

          // استدعاء وظيفة تحديث المخزون بعد الإرجاع
          const result = inventoryManager.updateInventoryAfterReturn(numericItemId, numericQuantity);

          if (!result.success) {
            const errorMsg = `فشل في تحديث المخزون بعد عملية الاسترجاع للصنف ${numericItemId}: ${result.error}`;
            logError(new Error(errorMsg), 'updateInventoryAfterTransaction');
            console.error(`[RETURN-DEBUG] ${errorMsg}`);
            return false;
          }

          logSystem(`[RETURN-DEBUG] تم تحديث المخزون بنجاح بعد عملية الاسترجاع للصنف ${numericItemId}. الكمية الجديدة: ${result.newQuantity}`, 'info');
          console.log(`[RETURN-DEBUG] تم تحديث المخزون بنجاح بعد عملية الاسترجاع للصنف ${numericItemId}. الكمية الجديدة: ${result.newQuantity}`);

          return true;
        } else {
          // إذا لم تكن الوظيفة موجودة، استخدم الطريقة القديمة
          logSystem(`[RETURN-DEBUG] وظيفة updateInventoryAfterReturn غير موجودة، استخدام الطريقة القديمة`, 'warning');
          console.warn(`[RETURN-DEBUG] وظيفة updateInventoryAfterReturn غير موجودة، استخدام الطريقة القديمة`);

          // حساب الكمية الجديدة (إضافة الكمية المسترجعة إلى المخزون)
          const newQuantity = inventory.current_quantity + numericQuantity;

          logSystem(`[RETURN-DEBUG] الكمية الجديدة بعد الإرجاع: ${newQuantity}`, 'info');
          console.log(`[RETURN-DEBUG] الكمية الجديدة بعد الإرجاع: ${newQuantity}`);

          // تحديث سجل المخزون مباشرة
          const updateStmt = db.prepare(`
            UPDATE inventory
            SET current_quantity = ?, last_updated = ?
            WHERE item_id = ?
          `);

          const result = updateStmt.run(
            newQuantity,
            now,
            numericItemId
          );

          // التحقق من نجاح التحديث
          if (result.changes <= 0) {
            const errorMsg = `فشل في تحديث المخزون بعد عملية الاسترجاع للصنف ${numericItemId}`;
            logError(new Error(errorMsg), 'updateInventoryAfterTransaction');
            console.error(`[RETURN-DEBUG] ${errorMsg}`);
            return false;
          }

          logSystem(`[RETURN-DEBUG] تم تحديث المخزون بنجاح بعد عملية الاسترجاع للصنف ${numericItemId}. الكمية الجديدة: ${newQuantity}`, 'info');
          console.log(`[RETURN-DEBUG] تم تحديث المخزون بنجاح بعد عملية الاسترجاع للصنف ${numericItemId}. الكمية الجديدة: ${newQuantity}`);

          // محاولة مسح التخزين المؤقت للمخزون
          try {
            if (typeof inventoryManager.clearInventoryCache === 'function') {
              inventoryManager.clearInventoryCache();
              logSystem(`[RETURN-DEBUG] تم مسح التخزين المؤقت للمخزون بعد عملية الاسترجاع`, 'info');
              console.log(`[RETURN-DEBUG] تم مسح التخزين المؤقت للمخزون بعد عملية الاسترجاع`);
            }
          } catch (cacheError) {
            logSystem(`[RETURN-DEBUG] تحذير: فشل في مسح التخزين المؤقت للمخزون: ${cacheError.message}`, 'warning');
            console.warn(`[RETURN-DEBUG] تحذير: فشل في مسح التخزين المؤقت للمخزون: ${cacheError.message}`);
          }

          // محاولة إرسال إشعار بتحديث المخزون
          try {
            eventSystem.notifyInventoryUpdated({
              itemId: numericItemId,
              name: inventory.name || 'صنف غير معروف',
              current_quantity: newQuantity,
              operation: 'return',
              quantity: numericQuantity
            });
            logSystem(`[RETURN-DEBUG] تم إرسال إشعار بتحديث المخزون بعد عملية الاسترجاع`, 'info');
            console.log(`[RETURN-DEBUG] تم إرسال إشعار بتحديث المخزون بعد عملية الاسترجاع`);
          } catch (notifyError) {
            logSystem(`[RETURN-DEBUG] تحذير: فشل في إرسال إشعار تحديث المخزون: ${notifyError.message}`, 'warning');
            console.warn(`[RETURN-DEBUG] تحذير: فشل في إرسال إشعار تحديث المخزون: ${notifyError.message}`);
          }

          return true;
        }
      } catch (error) {
        logError(error, 'updateInventoryAfterTransaction - تحديث المخزون بعد الإرجاع');
        console.error(`[RETURN-DEBUG] خطأ في تحديث المخزون بعد الإرجاع:`, error);
        return false;
      }
    } else {
      logError(new Error(`نوع المعاملة غير معروف: ${transactionType}`), 'updateInventoryAfterTransaction');
      return false;
    }
  } catch (error) {
    console.error('خطأ في تحديث المخزون بعد المعاملة:', error);
    logError(error, 'updateInventoryAfterTransaction');
    return false;
  }
}

/**
 * تحديث الخزينة بعد المعاملة
 * @param {string} transaction_type - نوع المعاملة
 * @param {number} totalPrice - السعر الإجمالي
 * @param {number} profit - الربح
 * @param {number} userId - معرف المستخدم
 * @param {number} transportCost - مصاريف النقل (اختياري)
 * @returns {Object} - نتيجة العملية
 */
function updateCashboxAfterTransaction(transaction_type, totalPrice, profit, userId, transportCost = 0) {
  try {
    console.log(`[CASHBOX-FIX] بدء تحديث الخزينة بعد المعاملة - النوع: ${transaction_type}, المبلغ: ${totalPrice}, الربح: ${profit}, مصاريف النقل: ${transportCost}, المستخدم: ${userId}`);
    logSystem(`[CASHBOX-FIX] بدء تحديث الخزينة بعد المعاملة - النوع: ${transaction_type}, المبلغ: ${totalPrice}, الربح: ${profit}, مصاريف النقل: ${transportCost}`, 'info');

    // تسجيل معلومات التشخيص
    console.log(`[CASHBOX-DEBUG] معلومات تشخيصية لتحديث الخزينة:`);
    console.log(`- نوع المعاملة: ${transaction_type}`);
    console.log(`- المبلغ الإجمالي: ${totalPrice} (النوع: ${typeof totalPrice})`);
    console.log(`- الربح: ${profit} (النوع: ${typeof profit})`);
    console.log(`- مصاريف النقل: ${transportCost} (النوع: ${typeof transportCost})`);
    console.log(`- معرف المستخدم: ${userId} (النوع: ${typeof userId})`);

    // التحقق من وجود قاعدة البيانات
    if (!db) {
      const errorMsg = 'قاعدة البيانات غير متصلة';
      console.error(`[CASHBOX-FIX] ${errorMsg}`);
      logError(new Error(errorMsg), 'updateCashboxAfterTransaction');
      return { success: false, error: errorMsg };
    }

    // التحويل إلى أرقام
    const numericTotalPrice = Number(totalPrice) || 0;
    const numericProfit = Number(profit) || 0;
    const numericTransportCost = Number(transportCost) || 0;

    // التحقق من صحة البيانات
    if (isNaN(numericTotalPrice) || numericTotalPrice < 0) {
      const errorMsg = `السعر الإجمالي غير صالح: ${totalPrice}`;
      console.error(`[CASHBOX-FIX] ${errorMsg}`);
      logError(new Error(errorMsg), 'updateCashboxAfterTransaction');
      return { success: false, error: errorMsg };
    }

    // التحقق من نوع المعاملة
    if (!transaction_type || (transaction_type !== 'sale' && transaction_type !== 'purchase' && transaction_type !== 'return')) {
      const errorMsg = `نوع المعاملة غير صالح: ${transaction_type}`;
      console.error(`[CASHBOX-FIX] ${errorMsg}`);
      logError(new Error(errorMsg), 'updateCashboxAfterTransaction');
      return { success: false, error: errorMsg };
    }

    // تاريخ التحديث
    const now = new Date().toISOString();

    // التحقق من وجود خزينة
    console.log(`[CASHBOX-DEBUG] التحقق من وجود خزينة...`);
    const checkCashboxStmt = db.prepare('SELECT * FROM cashbox LIMIT 1');
    const cashbox = checkCashboxStmt.get();

    // تسجيل نتيجة التحقق
    if (cashbox) {
      console.log(`[CASHBOX-DEBUG] تم العثور على الخزينة:`, {
        id: cashbox.id,
        initial_balance: cashbox.initial_balance,
        current_balance: cashbox.current_balance,
        profit_total: cashbox.profit_total,
        sales_total: cashbox.sales_total,
        purchases_total: cashbox.purchases_total,
        returns_total: cashbox.returns_total || 0
      });
    } else {
      console.log(`[CASHBOX-DEBUG] لم يتم العثور على الخزينة. سيتم إنشاء خزينة جديدة.`);
    }

    // إذا لم تكن هناك خزينة، قم بإنشاء واحدة
    if (!cashbox) {
      try {
        console.log(`[CASHBOX-DEBUG] إنشاء خزينة جديدة...`);
        const createCashboxStmt = db.prepare(`
          INSERT INTO cashbox (
            initial_balance, current_balance, profit_total, sales_total, purchases_total, returns_total, created_at, updated_at
          )
          VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        `);

        const createResult = createCashboxStmt.run(
          0, // الرصيد الافتتاحي
          0, // الرصيد الحالي
          0, // إجمالي الربح
          0, // إجمالي المبيعات
          0, // إجمالي المشتريات
          0, // إجمالي المرتجعات
          now,
          now
        );

        console.log(`[CASHBOX-DEBUG] تم إنشاء خزينة جديدة بنجاح:`, createResult);

        // التحقق من إنشاء الخزينة بنجاح
        const newCashboxStmt = db.prepare('SELECT * FROM cashbox LIMIT 1');
        const newCashbox = newCashboxStmt.get();

        if (newCashbox) {
          console.log(`[CASHBOX-DEBUG] تم التحقق من إنشاء الخزينة الجديدة:`, {
            id: newCashbox.id,
            initial_balance: newCashbox.initial_balance,
            current_balance: newCashbox.current_balance
          });
        } else {
          console.error(`[CASHBOX-DEBUG] فشل في التحقق من إنشاء الخزينة الجديدة!`);
        }
      } catch (createError) {
        console.error(`[CASHBOX-DEBUG] خطأ في إنشاء خزينة جديدة:`, createError);
        logError(createError, 'updateCashboxAfterTransaction - createCashbox');
        throw createError; // إعادة رمي الخطأ للتعامل معه في الدالة الأم
      }
    }

    // بدء معاملة قاعدة البيانات
    return db.transaction(() => {
      // استيراد وظائف الخزينة
      const cashboxUtils = require('./src/utils/cashboxUtils');

      if (transaction_type === 'sale') {
        console.log(`[CASHBOX-FIX] معاملة بيع - المبلغ: ${numericTotalPrice}, الربح: ${numericProfit}`);
        logSystem(`[CASHBOX-FIX] معاملة بيع - المبلغ: ${numericTotalPrice}, الربح: ${numericProfit}`, 'info');

        // التحقق من وجود الخزينة مرة أخرى بعد إنشائها إذا لم تكن موجودة
        const currentCashboxStmt = db.prepare('SELECT * FROM cashbox LIMIT 1');
        const currentCashbox = currentCashboxStmt.get();

        if (!currentCashbox) {
          const errorMsg = 'الخزينة غير موجودة بعد محاولة إنشائها';
          console.error(`[CASHBOX-FIX] ${errorMsg}`);
          logError(new Error(errorMsg), 'updateCashboxAfterTransaction - sale');
          throw new Error(errorMsg);
        }

        // في حالة البيع، نطبق قيد الرصيد الحالي وآلية التوزيع
        const currentBalance = currentCashbox.current_balance;
        const initialBalance = currentCashbox.initial_balance;
        const salesTotal = currentCashbox.sales_total;
        const profitTotal = currentCashbox.profit_total;

        console.log(`[CASHBOX-FIX] قيم الخزينة قبل التحديث - الرصيد الحالي: ${currentBalance}, الرصيد الافتتاحي: ${initialBalance}, إجمالي المبيعات: ${salesTotal}, إجمالي الأرباح: ${profitTotal}`);
        logSystem(`[CASHBOX-FIX] قيم الخزينة قبل التحديث - الرصيد الحالي: ${currentBalance}, الرصيد الافتتاحي: ${initialBalance}, إجمالي المبيعات: ${salesTotal}, إجمالي الأرباح: ${profitTotal}`, 'info');

        // استيراد وظائف الخزينة
        const cashboxUtils = require('./src/utils/cashboxUtils');

        // تطبيق آلية التوزيع: حساب كم من المبلغ يذهب للرصيد الحالي وكم للأرباح
        const { amountToAddToBalance, excessAmount } = cashboxUtils.calculateBalanceDistribution(
          numericTotalPrice,
          currentBalance,
          initialBalance
        );

        console.log(`[CASHBOX-FIX] آلية التوزيع - المبلغ للرصيد: ${amountToAddToBalance}, المبلغ الزائد للأرباح: ${excessAmount}`);
        logSystem(`[CASHBOX-FIX] آلية التوزيع - المبلغ للرصيد: ${amountToAddToBalance}, المبلغ الزائد للأرباح: ${excessAmount}`, 'info');

        console.log(`[CASHBOX-FIX] الربح من البيع: ${numericProfit}, المبلغ الزائد للأرباح: ${excessAmount}`);
        logSystem(`[CASHBOX-FIX] الربح من البيع: ${numericProfit}, المبلغ الزائد للأرباح: ${excessAmount}`, 'info');

        // تحديث الخزينة (بدون إضافة الربح مباشرة - سيتم حسابه من المعاملات لاحقاً)
        console.log(`[CASHBOX-FIX] تحديث الخزينة - إضافة للرصيد: ${amountToAddToBalance}, إضافة للمبيعات: ${numericTotalPrice}`);

        const updateStmt = db.prepare(`
          UPDATE cashbox
          SET current_balance = current_balance + ?,  -- إضافة المبلغ المحسوب إلى الرصيد الحالي
              sales_total = sales_total + ?,          -- إضافة المبلغ كاملاً إلى إجمالي المبيعات
              updated_at = ?                          -- تحديث وقت التحديث
          WHERE id = ?
        `);

        // تسجيل القيم قبل تنفيذ الاستعلام
        console.log(`[CASHBOX-DEBUG] قيم تحديث الخزينة:`, {
          amountToBalance: amountToAddToBalance,
          sales: numericTotalPrice,
          now,
          cashboxId: currentCashbox.id
        });

        let updateResult;
        try {
          console.log(`[CASHBOX-DEBUG] تنفيذ تحديث الخزينة...`);

          updateResult = updateStmt.run(
            amountToAddToBalance,  // إضافة المبلغ المحسوب إلى الرصيد الحالي
            numericTotalPrice,     // إضافة المبلغ كاملاً إلى إجمالي المبيعات
            now,                   // وقت التحديث
            currentCashbox.id      // معرف الخزينة
          );

          console.log(`[CASHBOX-DEBUG] نتيجة تحديث الخزينة:`, updateResult);
        } catch (updateError) {
          console.error(`[CASHBOX-DEBUG] خطأ في تحديث الخزينة:`, updateError);
          logError(updateError, 'updateCashboxAfterTransaction - updateCashbox');
          throw updateError;
        }

        console.log(`[CASHBOX-FIX] نتيجة تحديث الخزينة:`, updateResult);
        logSystem(`[CASHBOX-FIX] نتيجة تحديث الخزينة: ${JSON.stringify(updateResult)}`, 'info');

        // إضافة رسالة توضيحية عن التوزيع
        let message;
        if (excessAmount > 0) {
          message = `تم إضافة ${amountToAddToBalance} إلى الرصيد الحالي (المبلغ الزائد ${excessAmount} سيتم إضافته للأرباح تلقائياً)`;
        } else {
          message = `تم إضافة ${amountToAddToBalance} إلى الرصيد الحالي`;
        }
        console.log(`[CASHBOX-FIX] ${message}`);
        logSystem(message, 'info');

        // التحقق من التحديث
        const checkStmt = db.prepare('SELECT * FROM cashbox WHERE id = ?');
        const updatedCashboxAfterUpdate = checkStmt.get(cashbox ? cashbox.id : 1);
        console.log(`[CASHBOX-DEBUG] قيم الخزينة بعد التحديث - الرصيد الحالي: ${updatedCashboxAfterUpdate.current_balance}, إجمالي الربح: ${updatedCashboxAfterUpdate.profit_total}, إجمالي المبيعات: ${updatedCashboxAfterUpdate.sales_total}`);
        logSystem(`[CASHBOX-DEBUG] قيم الخزينة بعد التحديث - الرصيد الحالي: ${updatedCashboxAfterUpdate.current_balance}, إجمالي الربح: ${updatedCashboxAfterUpdate.profit_total}, إجمالي المبيعات: ${updatedCashboxAfterUpdate.sales_total}`, 'info');

        // إضافة معاملة للخزينة
        try {
          console.log(`[CASHBOX-DEBUG] إضافة معاملة للخزينة - النوع: income، المبلغ: ${numericTotalPrice}`);

          const addTransactionStmt = db.prepare(`
            INSERT INTO cashbox_transactions (
              type, amount, source, notes, user_id, created_at
            )
            VALUES (?, ?, ?, ?, ?, ?)
          `);

          const addTransactionResult = addTransactionStmt.run(
            'income',
            numericTotalPrice,
            'sale',
            `مبيعات بقيمة ${numericTotalPrice}`,
            userId,
            now
          );

          console.log(`[CASHBOX-DEBUG] تم إضافة معاملة للخزينة بنجاح:`, addTransactionResult);
        } catch (addTransactionError) {
          console.error(`[CASHBOX-DEBUG] خطأ في إضافة معاملة للخزينة:`, addTransactionError);
          logError(addTransactionError, 'updateCashboxAfterTransaction - addCashboxTransaction');
          // لا نريد إيقاف العملية إذا فشلت إضافة معاملة الخزينة
        }

        // الحصول على الخزينة المحدثة
        console.log(`[CASHBOX-DEBUG] الحصول على الخزينة المحدثة...`);
        const getCashboxStmt = db.prepare('SELECT * FROM cashbox LIMIT 1');
        const updatedCashbox = getCashboxStmt.get();

        // التحقق من أن updatedCashbox تم استرجاعه بنجاح
        if (!updatedCashbox) {
          const errorMsg = 'فشل في استرجاع بيانات الخزينة المحدثة. تأكد من وجود خزينة في قاعدة البيانات.';
          console.error(`[CASHBOX-ERROR] ${errorMsg}`);
          logError(new Error(errorMsg), 'updateCashboxAfterTransaction - getCashboxAfterUpdate');
          throw new Error(errorMsg);
        }

        console.log(`[CASHBOX-DEBUG] تم الحصول على الخزينة المحدثة بنجاح:`, {
          id: updatedCashbox.id,
          current_balance: updatedCashbox.current_balance,
          profit_total: updatedCashbox.profit_total,
          sales_total: updatedCashbox.sales_total,
          purchases_total: updatedCashbox.purchases_total,
          returns_total: updatedCashbox.returns_total || 0
        });

        // تحديث إجمالي الأرباح في الخزينة تلقائياً وفورياً
        try {
          console.log(`[PROFIT-FIX] تحديث إجمالي الأرباح تلقائياً بعد معاملة البيع...`);

          // إعادة حساب إجمالي الأرباح باستخدام الدالة المحسنة
          const calculatedTotalProfit = recalculateTotalProfits();
          console.log(`[PROFIT-FIX] إجمالي الأرباح المحسوب من المعاملات: ${calculatedTotalProfit}`);

          // تحديث إجمالي الأرباح في قاعدة البيانات باستخدام الدالة المحسنة
          const profitUpdateSuccess = updateProfitTotalInDatabase(calculatedTotalProfit);

          if (profitUpdateSuccess) {
            // تحديث قيمة الأرباح في الكائن المحدث
            updatedCashbox.profit_total = calculatedTotalProfit;
            console.log(`[PROFIT-FIX] تم تحديث profit_total في الكائن إلى: ${calculatedTotalProfit}`);
          } else {
            console.error(`[PROFIT-FIX] فشل في تحديث profit_total في قاعدة البيانات`);
          }

        } catch (profitUpdateError) {
          console.error(`[PROFIT-FIX] خطأ في التحديث التلقائي للأرباح:`, profitUpdateError);
          logError(profitUpdateError, 'updateCashboxAfterTransaction - auto profit update');
        }

        // إرسال إشعار بتحديث الخزينة بعد عملية البيع
        try {
          console.log(`[CASHBOX-DEBUG] إرسال إشعار بتحديث الخزينة بعد عملية البيع...`);

          const notifyData = {
            id: updatedCashbox.id,
            current_balance: updatedCashbox.current_balance,
            sales_total: updatedCashbox.sales_total,
            purchases_total: updatedCashbox.purchases_total,
            returns_total: updatedCashbox.returns_total || 0,
            transport_total: updatedCashbox.transport_total || 0,
            profit_total: updatedCashbox.profit_total,
            transaction_type: 'sale',
            amount: numericTotalPrice,
            profit: numericProfit,  // الربح من المعاملة
            excess_amount: excessAmount,  // المبلغ الزائد المحول للأرباح
            amount_to_balance: amountToAddToBalance,  // المبلغ المضاف للرصيد
            success: true,
            instant_update: true // علامة للتحديث الفوري
          };

          console.log(`[PROFIT-FIX] إرسال profit_total في الإشعار: ${updatedCashbox.profit_total}`);

          console.log(`[CASHBOX-DEBUG] بيانات الإشعار:`, notifyData);

          eventSystem.notifyCashboxUpdated(notifyData);

          console.log(`[CASHBOX-DEBUG] تم إرسال إشعار بتحديث الخزينة بنجاح`);

          // إرسال إشعار خاص لتحديث الأرباح في التقارير
          try {
            console.log(`[PROFITS-UPDATE] إرسال إشعار لتحديث الأرباح في التقارير...`);
            eventSystem.notifyProfitsUpdated({
              transaction_type: 'sale',
              amount: numericTotalPrice,
              profit: numericProfit,  // الربح من المعاملة
              excess_amount: excessAmount,  // المبلغ الزائد المحول للأرباح
              total_profit: updatedCashbox.profit_total,  // إجمالي الأرباح المحدث
              auto_update: true,  // تحديث تلقائي
              timestamp: new Date().toISOString()
            });
            console.log(`[PROFITS-UPDATE] تم إرسال إشعار تحديث الأرباح بنجاح`);

            // إرسال إشعار إضافي للتحديث المباشر
            try {
              eventSystem.sendEvent('direct-update', {
                transaction_type: 'sale',
                amount: numericTotalPrice,
                profit: numericProfit,
                total_profit: updatedCashbox.profit_total,
                timestamp: new Date().toISOString()
              });
              console.log(`[PROFITS-UPDATE] تم إرسال إشعار التحديث المباشر بنجاح`);
            } catch (directUpdateError) {
              console.error(`[PROFITS-UPDATE] خطأ في إرسال إشعار التحديث المباشر:`, directUpdateError);
            }

            // إرسال إشعار إضافي للتحديث التلقائي
            try {
              eventSystem.sendEvent('auto-profits-updated', {
                transaction_type: 'sale',
                amount: numericTotalPrice,
                profit: numericProfit,
                total_profit: updatedCashbox.profit_total,
                auto_update: true,
                timestamp: new Date().toISOString()
              });
              console.log(`[PROFITS-UPDATE] تم إرسال إشعار التحديث التلقائي بنجاح`);
            } catch (autoUpdateError) {
              console.error(`[PROFITS-UPDATE] خطأ في إرسال إشعار التحديث التلقائي:`, autoUpdateError);
            }
          } catch (profitsNotifyError) {
            console.error(`[PROFITS-UPDATE] خطأ في إرسال إشعار تحديث الأرباح:`, profitsNotifyError);
          }
        } catch (notifyError) {
          console.error(`[CASHBOX-DEBUG] خطأ في إرسال إشعار تحديث الخزينة:`, notifyError);
          logError(notifyError, 'updateCashboxAfterTransaction - notifyCashboxUpdated');
          // لا نريد إيقاف العملية إذا فشل إرسال الإشعار
        }

        return { success: true };
      } else if (transaction_type === 'purchase') {
        console.log(`[CASHBOX-FIX] معاملة شراء - المبلغ: ${numericTotalPrice}`);
        logSystem(`[CASHBOX-FIX] معاملة شراء - المبلغ: ${numericTotalPrice}`, 'info');

        // في حالة الشراء، نخصم المبلغ من الرصيد الحالي وتحديث إجمالي المشتريات
        const currentBalance = cashbox ? cashbox.current_balance : 0;
        const purchasesTotal = cashbox ? cashbox.purchases_total : 0;
        const salesTotal = cashbox ? cashbox.sales_total : 0;
        const profitTotal = cashbox ? cashbox.profit_total : 0;

        console.log(`[CASHBOX-FIX] قيم الخزينة قبل التحديث - الرصيد الحالي: ${currentBalance}, إجمالي المشتريات: ${purchasesTotal}, إجمالي المبيعات: ${salesTotal}, إجمالي الأرباح: ${profitTotal}`);
        logSystem(`[CASHBOX-FIX] قيم الخزينة قبل التحديث - الرصيد الحالي: ${currentBalance}, إجمالي المشتريات: ${purchasesTotal}, إجمالي المبيعات: ${salesTotal}, إجمالي الأرباح: ${profitTotal}`, 'info');

        // التحقق من أن الرصيد الحالي كافٍ للشراء
        if (currentBalance < numericTotalPrice) {
          console.log(`[CASHBOX-FIX] تحذير: الرصيد الحالي (${currentBalance}) أقل من مبلغ الشراء (${numericTotalPrice})`);
          logSystem(`[CASHBOX-FIX] تحذير: الرصيد الحالي (${currentBalance}) أقل من مبلغ الشراء (${numericTotalPrice})`, 'warning');
          // نستمر في العملية رغم ذلك، لكن نضيف تحذيرًا
        }

        // حساب المبلغ الإجمالي للخصم من الرصيد (الشراء + مصاريف النقل)
        const totalDeductionAmount = numericTotalPrice + numericTransportCost;

        console.log(`[CASHBOX-FIX] تحديث الخزينة في حالة الشراء:`);
        console.log(`[CASHBOX-FIX] - مبلغ الشراء: ${numericTotalPrice}`);
        console.log(`[CASHBOX-FIX] - مصاريف النقل: ${numericTransportCost}`);
        console.log(`[CASHBOX-FIX] - إجمالي الخصم من الرصيد: ${totalDeductionAmount}`);
        console.log(`[CASHBOX-FIX] - إضافة للمشتريات: ${numericTotalPrice}`);
        console.log(`[CASHBOX-FIX] - إضافة لمصاريف النقل: ${numericTransportCost}`);

        const updateStmt = db.prepare(`
          UPDATE cashbox
          SET current_balance = current_balance - ?,
              purchases_total = purchases_total + ?,
              transport_total = transport_total + ?,
              updated_at = ?
          WHERE id = ?
        `);

        const updateResult = updateStmt.run(
          totalDeductionAmount,  // خصم المبلغ الإجمالي (شراء + نقل) مرة واحدة فقط
          numericTotalPrice,     // إضافة مبلغ الشراء فقط لإجمالي المشتريات
          numericTransportCost,  // إضافة مصاريف النقل لإجمالي مصاريف النقل
          now,
          cashbox ? cashbox.id : 1
        );

        console.log(`[CASHBOX-FIX] نتيجة تحديث الخزينة:`, updateResult);
        logSystem(`[CASHBOX-FIX] نتيجة تحديث الخزينة: ${JSON.stringify(updateResult)}`, 'info');

        // التحقق من التحديث
        const checkStmt = db.prepare('SELECT * FROM cashbox WHERE id = ?');
        const updatedCashboxAfterUpdate = checkStmt.get(cashbox ? cashbox.id : 1);
        console.log(`[CASHBOX-FIX] قيم الخزينة بعد التحديث:`);
        console.log(`[CASHBOX-FIX] - الرصيد الحالي: ${updatedCashboxAfterUpdate.current_balance}`);
        console.log(`[CASHBOX-FIX] - إجمالي المشتريات: ${updatedCashboxAfterUpdate.purchases_total}`);
        console.log(`[CASHBOX-FIX] - إجمالي مصاريف النقل: ${updatedCashboxAfterUpdate.transport_total}`);
        console.log(`[CASHBOX-FIX] ✅ تم إصلاح مشكلة الخصم المزدوج لمصاريف النقل`);
        logSystem(`[CASHBOX-FIX] قيم الخزينة بعد التحديث - الرصيد الحالي: ${updatedCashboxAfterUpdate.current_balance}, إجمالي المشتريات: ${updatedCashboxAfterUpdate.purchases_total}, إجمالي مصاريف النقل: ${updatedCashboxAfterUpdate.transport_total}`, 'info');

        // إضافة معاملة للخزينة للمشتريات
        const addTransactionStmt = db.prepare(`
          INSERT INTO cashbox_transactions (
            type, amount, source, notes, user_id, created_at
          )
          VALUES (?, ?, ?, ?, ?, ?)
        `);

        // تحديد نص الملاحظة بناءً على وجود مصاريف النقل
        const purchaseNotes = numericTransportCost > 0
          ? `مشتريات بقيمة ${numericTotalPrice} + مصاريف نقل ${numericTransportCost} = إجمالي ${totalDeductionAmount}`
          : `مشتريات بقيمة ${numericTotalPrice}`;

        addTransactionStmt.run(
          'expense',
          totalDeductionAmount,  // استخدام المبلغ الإجمالي (شراء + نقل)
          'purchase',
          purchaseNotes,
          userId,
          now
        );

        // إضافة معاملة منفصلة لمصاريف النقل إذا كانت موجودة (للسجل فقط)
        // ملاحظة: مصاريف النقل تم خصمها بالفعل من الرصيد الحالي في التحديث أعلاه
        if (numericTransportCost > 0) {
          console.log(`[CASHBOX-FIX] إضافة معاملة مصاريف النقل للسجل - المبلغ: ${numericTransportCost}`);
          console.log(`[CASHBOX-FIX] ملاحظة: مصاريف النقل تم خصمها بالفعل من الرصيد الحالي في التحديث السابق`);

          // إضافة معاملة مصاريف النقل للخزينة (للسجل فقط - بدون تأثير على الرصيد)
          const addTransportTransactionStmt = db.prepare(`
            INSERT INTO cashbox_transactions (
              type, amount, source, notes, user_id, created_at
            )
            VALUES (?, ?, ?, ?, ?, ?)
          `);

          addTransportTransactionStmt.run(
            'expense',
            numericTransportCost,
            'transport',
            `مصاريف نقل بقيمة ${numericTransportCost} (مشتريات) - مدرجة ضمن إجمالي الشراء`,
            userId,
            now
          );

          console.log(`[CASHBOX-FIX] تم إضافة معاملة مصاريف النقل للسجل بنجاح`);
        }

        // الحصول على الخزينة المحدثة
        const getCashboxStmt = db.prepare('SELECT * FROM cashbox LIMIT 1');
        const updatedCashbox = getCashboxStmt.get();

        // التحقق من أن updatedCashbox تم استرجاعه بنجاح
        if (!updatedCashbox) {
          console.error('[ERROR] فشل في استرجاع بيانات الخزينة المحدثة. تأكد من وجود خزينة في قاعدة البيانات.');
          throw new Error('فشل في استرجاع بيانات الخزينة المحدثة');
        }

        // تحديث إجمالي الأرباح تلقائياً بعد معاملة الشراء
        // ملاحظة: عمليات الشراء لا تؤثر على الأرباح مباشرة، لكن قد تؤثر على حساب متوسط التكلفة
        // مما قد يؤثر على حساب الأرباح في المبيعات المستقبلية
        try {
          console.log(`[PROFIT-FIX] تحديث إجمالي الأرباح تلقائياً بعد معاملة الشراء...`);
          console.log(`[PROFIT-FIX] ملاحظة: تكاليف النقل في المشتريات تخصم من الرصيد الحالي فقط ولا تؤثر على الأرباح`);

          // إعادة حساب إجمالي الأرباح باستخدام الدالة المحسنة
          // هذا يحسب الأرباح من معاملات البيع فقط
          const calculatedTotalProfit = recalculateTotalProfits();
          console.log(`[PROFIT-FIX] إجمالي الأرباح المحسوب بعد الشراء (من البيع فقط): ${calculatedTotalProfit}`);

          // تحديث إجمالي الأرباح في قاعدة البيانات باستخدام الدالة المحسنة
          const profitUpdateSuccess = updateProfitTotalInDatabase(calculatedTotalProfit);

          if (profitUpdateSuccess) {
            // تحديث قيمة الأرباح في الكائن المحدث
            updatedCashbox.profit_total = calculatedTotalProfit;
            console.log(`[PROFIT-FIX] تم تحديث profit_total بعد الشراء إلى: ${calculatedTotalProfit}`);
          } else {
            console.error(`[PROFIT-FIX] فشل في تحديث profit_total بعد الشراء`);
          }

        } catch (profitUpdateError) {
          console.error(`[PROFIT-FIX] خطأ في التحديث التلقائي للأرباح بعد الشراء:`, profitUpdateError);
          logError(profitUpdateError, 'updateCashboxAfterTransaction - auto profit update after purchase');
        }

        // إرسال إشعار بتحديث الخزينة بعد عملية الشراء
        eventSystem.notifyCashboxUpdated({
          id: updatedCashbox ? updatedCashbox.id : 1,
          current_balance: updatedCashbox ? updatedCashbox.current_balance : 0,
          sales_total: updatedCashbox ? updatedCashbox.sales_total : 0,
          purchases_total: updatedCashbox ? updatedCashbox.purchases_total : 0,
          returns_total: updatedCashbox ? updatedCashbox.returns_total : 0,
          transport_total: updatedCashbox ? updatedCashbox.transport_total : 0,
          profit_total: updatedCashbox ? updatedCashbox.profit_total : 0,
          transaction_type: 'purchase',
          amount: numericTotalPrice,
          transport_cost: numericTransportCost,
          profit: 0,
          success: true
        });

        // إرسال إشعار إضافي للتحديث المباشر
        try {
          eventSystem.sendEvent('direct-update', {
            transaction_type: 'purchase',
            amount: numericTotalPrice,
            transport_cost: numericTransportCost,
            total_profit: updatedCashbox.profit_total,
            timestamp: new Date().toISOString()
          });
          console.log(`[PURCHASE-UPDATE] تم إرسال إشعار التحديث المباشر للشراء بنجاح`);
        } catch (directUpdateError) {
          console.error(`[PURCHASE-UPDATE] خطأ في إرسال إشعار التحديث المباشر للشراء:`, directUpdateError);
        }

        // إرسال إشعار خاص لتحديث الأرباح في التقارير بعد الشراء
        try {
          console.log(`[PROFITS-UPDATE] إرسال إشعار لتحديث الأرباح بعد الشراء...`);
          eventSystem.notifyProfitsUpdated({
            transaction_type: 'purchase',
            amount: numericTotalPrice,
            transport_cost: numericTransportCost,
            profit: 0,  // الشراء لا يحتوي على ربح مباشر
            total_profit: updatedCashbox.profit_total,  // إجمالي الأرباح المحدث
            auto_update: true,  // تحديث تلقائي
            timestamp: new Date().toISOString()
          });
          console.log(`[PROFITS-UPDATE] تم إرسال إشعار تحديث الأرباح بعد الشراء بنجاح`);

          // إرسال إشعار إضافي للتحديث التلقائي
          try {
            eventSystem.sendEvent('auto-profits-updated', {
              transaction_type: 'purchase',
              amount: numericTotalPrice,
              transport_cost: numericTransportCost,
              profit: 0,
              total_profit: updatedCashbox.profit_total,
              auto_update: true,
              timestamp: new Date().toISOString()
            });
            console.log(`[PROFITS-UPDATE] تم إرسال إشعار التحديث التلقائي للأرباح بعد الشراء بنجاح`);
          } catch (autoUpdateError) {
            console.error(`[PROFITS-UPDATE] خطأ في إرسال إشعار التحديث التلقائي للأرباح بعد الشراء:`, autoUpdateError);
          }
        } catch (profitsNotifyError) {
          console.error(`[PROFITS-UPDATE] خطأ في إرسال إشعار تحديث الأرباح بعد الشراء:`, profitsNotifyError);
        }

        return { success: true };
      } else if (transaction_type === 'return') {
        console.log(`[CASHBOX-FIX] معاملة استرجاع - المبلغ: ${numericTotalPrice}`);
        logSystem(`[CASHBOX-FIX] معاملة استرجاع - المبلغ: ${numericTotalPrice}`, 'info');

        // في حالة الاسترجاع، نخصم المبلغ من الرصيد الحالي (لأننا نعيد المال للعميل)
        // ونخصم من إجمالي المبيعات وزيادة إجمالي المرتجعات
        const currentBalance = cashbox ? cashbox.current_balance : 0;
        const salesTotal = cashbox ? cashbox.sales_total : 0;
        const returnsTotal = cashbox ? cashbox.returns_total : 0;
        const profitTotal = cashbox ? cashbox.profit_total : 0;
        const purchasesTotal = cashbox ? cashbox.purchases_total : 0;

        console.log(`[CASHBOX-FIX] قيم الخزينة قبل التحديث - الرصيد الحالي: ${currentBalance}, إجمالي المبيعات: ${salesTotal}, إجمالي المشتريات: ${purchasesTotal}, إجمالي المرتجعات: ${returnsTotal}, إجمالي الأرباح: ${profitTotal}`);
        logSystem(`[CASHBOX-FIX] قيم الخزينة قبل التحديث - الرصيد الحالي: ${currentBalance}, إجمالي المبيعات: ${salesTotal}, إجمالي المشتريات: ${purchasesTotal}, إجمالي المرتجعات: ${returnsTotal}, إجمالي الأرباح: ${profitTotal}`, 'info');

        // إضافة معاملة للخزينة
        try {
          console.log(`[CASHBOX-DEBUG] إضافة معاملة للخزينة...`);

          const insertStmt = db.prepare(`
            INSERT INTO cashbox_transactions (
              type, amount, source, notes, user_id, created_at
            )
            VALUES (?, ?, ?, ?, ?, ?)
          `);

          const addTransactionResult = insertStmt.run(
            'expense',
            numericTotalPrice,
            'return',
            `استرجاع بقيمة ${numericTotalPrice}`,
            userId || null,
            now
          );

          console.log(`[CASHBOX-DEBUG] تم إضافة معاملة للخزينة بنجاح:`, addTransactionResult);
        } catch (addTransactionError) {
          console.error(`[CASHBOX-DEBUG] خطأ في إضافة معاملة للخزينة:`, addTransactionError);
          logError(addTransactionError, 'updateCashboxAfterTransaction - addCashboxTransaction');
          // لا نريد إيقاف العملية إذا فشلت إضافة معاملة الخزينة
        }

        // التحقق من أن إجمالي المبيعات كافٍ للاسترجاع
        if (salesTotal < numericTotalPrice) {
          console.log(`[CASHBOX-FIX] تحذير: إجمالي المبيعات (${salesTotal}) أقل من مبلغ الاسترجاع (${numericTotalPrice})`);
          logSystem(`[CASHBOX-FIX] تحذير: إجمالي المبيعات (${salesTotal}) أقل من مبلغ الاسترجاع (${numericTotalPrice})`, 'warning');
          // نستمر في العملية رغم ذلك، لكن نضيف تحذيرًا
        }

        // تقدير الربح المرتبط بالمبيعات المرتجعة (20% من قيمة المبيعات إذا لم يتم تحديده)
        const returnProfit = numericProfit > 0 ? numericProfit : (numericTotalPrice * 0.2);

        console.log(`[CASHBOX-FIX] تحديث الخزينة في حالة الاسترجاع - خصم من الرصيد: ${numericTotalPrice}, خصم من المبيعات: ${numericTotalPrice}, إضافة للمرتجعات: ${numericTotalPrice}, خصم من الأرباح: ${returnProfit}`);

        const updateStmt = db.prepare(`
          UPDATE cashbox
          SET current_balance = current_balance - ?,
              sales_total = sales_total - ?,
              returns_total = returns_total + ?,
              profit_total = CASE WHEN profit_total - ? < 0 THEN 0 ELSE profit_total - ? END,
              updated_at = ?
          WHERE id = ?
        `);

        const updateResult = updateStmt.run(
          numericTotalPrice,
          numericTotalPrice,
          numericTotalPrice,
          returnProfit,
          returnProfit,
          now,
          cashbox ? cashbox.id : 1
        );

        console.log(`[CASHBOX-FIX] نتيجة تحديث الخزينة:`, updateResult);
        logSystem(`[CASHBOX-FIX] نتيجة تحديث الخزينة: ${JSON.stringify(updateResult)}`, 'info');

        // التحقق من التحديث
        const checkStmt = db.prepare('SELECT * FROM cashbox WHERE id = ?');
        const updatedCashboxAfterUpdate = checkStmt.get(cashbox ? cashbox.id : 1);
        console.log(`[CASHBOX-FIX] قيم الخزينة بعد التحديث - الرصيد الحالي: ${updatedCashboxAfterUpdate.current_balance}, إجمالي المبيعات: ${updatedCashboxAfterUpdate.sales_total}, إجمالي المرتجعات: ${updatedCashboxAfterUpdate.returns_total}`);
        logSystem(`[CASHBOX-FIX] قيم الخزينة بعد التحديث - الرصيد الحالي: ${updatedCashboxAfterUpdate.current_balance}, إجمالي المبيعات: ${updatedCashboxAfterUpdate.sales_total}, إجمالي المرتجعات: ${updatedCashboxAfterUpdate.returns_total}`, 'info');

        // إضافة معاملة للخزينة
        const addTransactionStmt = db.prepare(`
          INSERT INTO cashbox_transactions (
            type, amount, source, notes, user_id, created_at
          )
          VALUES (?, ?, ?, ?, ?, ?)
        `);

        addTransactionStmt.run(
          'expense',
          numericTotalPrice,
          'return',
          `استرجاع منتجات بقيمة ${numericTotalPrice}`,
          userId,
          now
        );

        // الحصول على الخزينة المحدثة
        const getCashboxStmt = db.prepare('SELECT * FROM cashbox LIMIT 1');
        const updatedCashbox = getCashboxStmt.get();

        // التحقق من أن updatedCashbox تم استرجاعه بنجاح
        if (!updatedCashbox) {
          console.error('[ERROR] فشل في استرجاع بيانات الخزينة المحدثة. تأكد من وجود خزينة في قاعدة البيانات.');
          throw new Error('فشل في استرجاع بيانات الخزينة المحدثة');
        }

        // تحديث إجمالي الأرباح تلقائياً بعد معاملة الاسترجاع
        try {
          console.log(`[PROFIT-FIX] تحديث إجمالي الأرباح تلقائياً بعد معاملة الاسترجاع...`);

          // إعادة حساب إجمالي الأرباح باستخدام الدالة المحسنة
          const calculatedTotalProfit = recalculateTotalProfits();
          console.log(`[PROFIT-FIX] إجمالي الأرباح المحسوب بعد الاسترجاع: ${calculatedTotalProfit}`);

          // تحديث إجمالي الأرباح في قاعدة البيانات باستخدام الدالة المحسنة
          const profitUpdateSuccess = updateProfitTotalInDatabase(calculatedTotalProfit);

          if (profitUpdateSuccess) {
            // تحديث قيمة الأرباح في الكائن المحدث
            updatedCashbox.profit_total = calculatedTotalProfit;
            console.log(`[PROFIT-FIX] تم تحديث profit_total بعد الاسترجاع إلى: ${calculatedTotalProfit}`);
          } else {
            console.error(`[PROFIT-FIX] فشل في تحديث profit_total بعد الاسترجاع`);
          }

        } catch (profitUpdateError) {
          console.error(`[PROFIT-FIX] خطأ في التحديث التلقائي للأرباح بعد الاسترجاع:`, profitUpdateError);
          logError(profitUpdateError, 'updateCashboxAfterTransaction - auto profit update after return');
        }

        // إرسال إشعار بتحديث الخزينة بعد عملية الاسترجاع
        eventSystem.notifyCashboxUpdated({
          id: updatedCashbox ? updatedCashbox.id : 1,
          current_balance: updatedCashbox ? updatedCashbox.current_balance : 0,
          sales_total: updatedCashbox ? updatedCashbox.sales_total : 0,
          purchases_total: updatedCashbox ? updatedCashbox.purchases_total : 0,
          returns_total: updatedCashbox ? updatedCashbox.returns_total : 0,
          transport_total: updatedCashbox ? updatedCashbox.transport_total : 0,
          profit_total: updatedCashbox ? updatedCashbox.profit_total : 0,
          transaction_type: 'return',
          amount: numericTotalPrice,
          profit: -returnProfit, // إرسال الربح كقيمة سالبة لتوضيح أنه خصم من الأرباح
          success: true
        });

        // إرسال إشعار إضافي للتحديث المباشر
        try {
          eventSystem.sendEvent('direct-update', {
            transaction_type: 'return',
            amount: numericTotalPrice,
            profit: -returnProfit,
            total_profit: updatedCashbox.profit_total,
            timestamp: new Date().toISOString()
          });
          console.log(`[RETURN-UPDATE] تم إرسال إشعار التحديث المباشر للإرجاع بنجاح`);
        } catch (directUpdateError) {
          console.error(`[RETURN-UPDATE] خطأ في إرسال إشعار التحديث المباشر للإرجاع:`, directUpdateError);
        }

        // إرسال إشعار خاص لتحديث الأرباح في التقارير بعد الاسترجاع
        try {
          console.log(`[PROFITS-UPDATE] إرسال إشعار لتحديث الأرباح بعد الاسترجاع...`);
          eventSystem.notifyProfitsUpdated({
            transaction_type: 'return',
            amount: numericTotalPrice,
            profit: -returnProfit,  // الربح المخصوم بسبب الاسترجاع
            total_profit: updatedCashbox.profit_total,  // إجمالي الأرباح المحدث
            auto_update: true,  // تحديث تلقائي
            timestamp: new Date().toISOString()
          });
          console.log(`[PROFITS-UPDATE] تم إرسال إشعار تحديث الأرباح بعد الاسترجاع بنجاح`);

          // إرسال إشعار إضافي للتحديث التلقائي
          try {
            eventSystem.sendEvent('auto-profits-updated', {
              transaction_type: 'return',
              amount: numericTotalPrice,
              profit: -returnProfit,
              total_profit: updatedCashbox.profit_total,
              auto_update: true,
              timestamp: new Date().toISOString()
            });
            console.log(`[PROFITS-UPDATE] تم إرسال إشعار التحديث التلقائي للأرباح بعد الاسترجاع بنجاح`);
          } catch (autoUpdateError) {
            console.error(`[PROFITS-UPDATE] خطأ في إرسال إشعار التحديث التلقائي للأرباح بعد الاسترجاع:`, autoUpdateError);
          }
        } catch (profitsNotifyError) {
          console.error(`[PROFITS-UPDATE] خطأ في إرسال إشعار تحديث الأرباح بعد الاسترجاع:`, profitsNotifyError);
        }

        logSystem(`تم تحديث الخزينة بعد عملية الاسترجاع بقيمة ${numericTotalPrice}`, 'info');
        return { success: true };
      }

      // إرسال إشعار عام بالحاجة للتحديث (للحالات غير المعالجة)
      console.log(`[CASHBOX-FIX] نوع المعاملة غير معروف: ${transaction_type}، إرسال إشعار عام بالحاجة للتحديث`);
      logSystem(`[CASHBOX-FIX] نوع المعاملة غير معروف: ${transaction_type}، إرسال إشعار عام بالحاجة للتحديث`, 'warning');

      eventSystem.sendEvent(eventSystem.EventTypes.REFRESH_NEEDED, {
        target: 'cashbox',
        timestamp: new Date().toISOString(),
        transaction_type: transaction_type || 'unknown'
      });

      return { success: true, warning: `نوع المعاملة غير معروف: ${transaction_type}` };
    })();
  } catch (error) {
    console.error('[CASHBOX-FIX] خطأ في تحديث الخزينة بعد المعاملة:', error);
    logError(error, 'updateCashboxAfterTransaction');
    return { success: false, error: error.message };
  }
}

/**
 * إنشاء معرف فريد للمعاملة
 * @param {string} type - نوع المعاملة
 * @returns {string} - معرف المعاملة
 */
function generateTransactionId(type) {
  const date = new Date();
  const year = date.getFullYear().toString().slice(-2);
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const day = date.getDate().toString().padStart(2, '0');
  const hours = date.getHours().toString().padStart(2, '0');
  const minutes = date.getMinutes().toString().padStart(2, '0');
  const seconds = date.getSeconds().toString().padStart(2, '0');
  const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');

  let prefix = '';
  switch(type) {
    case 'purchase': prefix = 'PUR'; break;
    case 'sale': prefix = 'SAL'; break;
    case 'receiving': prefix = 'REC'; break;
    case 'withdrawal': prefix = 'WIT'; break;
    case 'return': prefix = 'RET'; break;
    default: prefix = 'TRX';
  }

  return `${prefix}-${year}${month}${day}-${hours}${minutes}${seconds}-${random}`;
}

/**
 * الحصول على المعاملات
 * @param {Object} filters - فلاتر التصفية
 * @returns {Array} - قائمة المعاملات
 */
function getTransactions(filters = null) {
  try {
    if (!db) throw new Error('قاعدة البيانات غير مهيأة');

    console.log('الحصول على المعاملات مع الفلاتر:', filters);

    // بناء استعلام مع الفلاتر
    let query = `
      SELECT t.*, i.name as item_name, i.unit, u.username as user_name,
             inv.minimum_quantity, inv.selling_price, inv.avg_price
      FROM transactions t
      LEFT JOIN items i ON t.item_id = i.id
      LEFT JOIN users u ON t.user_id = u.id
      LEFT JOIN inventory inv ON t.item_id = inv.item_id
    `;

    const params = [];
    const whereConditions = [];

    // إضافة الفلاتر إذا كانت موجودة
    if (filters) {
      // فلتر نوع المعاملة
      if (filters.transaction_type) {
        whereConditions.push('t.transaction_type = ?');
        params.push(filters.transaction_type);
      }

      // فلتر معرف الصنف
      if (filters.item_id) {
        whereConditions.push('t.item_id = ?');
        params.push(filters.item_id);
      }

      // فلتر معرف العميل
      if (filters.customer_id) {
        whereConditions.push('t.customer_id = ?');
        params.push(filters.customer_id);
      }

      // فلتر تاريخ البداية
      if (filters.start_date) {
        whereConditions.push('t.transaction_date >= ?');
        params.push(filters.start_date);
      }

      // فلتر تاريخ النهاية
      if (filters.end_date) {
        whereConditions.push('t.transaction_date <= ?');
        params.push(filters.end_date);
      }
    }

    // إضافة شروط WHERE إذا كانت موجودة
    if (whereConditions.length > 0) {
      query += ' WHERE ' + whereConditions.join(' AND ');
    }

    // إضافة الترتيب
    query += ' ORDER BY t.transaction_date DESC';

    // تنفيذ الاستعلام
    const stmt = db.prepare(query);
    const transactions = stmt.all(...params);

    // تحويل المعرفات إلى نصوص للتوافق مع الواجهة الأمامية
    const formattedTransactions = transactions.map(transaction => ({
      ...transaction,
      id: transaction.id.toString(),
      _id: transaction.id.toString(),
      item_id: transaction.item_id.toString(),
      customer_id: transaction.customer_id ? transaction.customer_id.toString() : null,
      user_id: transaction.user_id ? transaction.user_id.toString() : null
    }));

    console.log(`تم الحصول على ${formattedTransactions.length} معاملة بنجاح`);

    return formattedTransactions;
  } catch (error) {
    console.error('خطأ في الحصول على المعاملات:', error);
    logError(error, 'getTransactions');
    return [];
  }
}

/**
 * إصلاح قيم الخزينة السالبة
 * @returns {Object} - نتيجة الإصلاح
 */
function fixNegativeCashboxValues() {
  try {
    if (!db) throw new Error('قاعدة البيانات غير مهيأة');

    console.log('[CASHBOX-FIX] بدء إصلاح قيم الخزينة السالبة...');
    logSystem('[CASHBOX-FIX] بدء إصلاح قيم الخزينة السالبة...', 'info');

    // الحصول على الخزينة الحالية
    const getCashboxStmt = db.prepare('SELECT * FROM cashbox LIMIT 1');
    const cashbox = getCashboxStmt.get();

    if (!cashbox) {
      console.error('[CASHBOX-FIX] لا توجد خزينة! يجب إنشاء خزينة أولاً.');
      logSystem('[CASHBOX-FIX] لا توجد خزينة! يجب إنشاء خزينة أولاً.', 'error');
      return { success: false, message: 'لا توجد خزينة! يجب إنشاء خزينة أولاً.' };
    }

    // استيراد وظائف الخزينة
    const cashboxUtils = require('./src/utils/cashboxUtils');

    // عرض قيم الخزينة قبل الإصلاح
    console.log('[CASHBOX-FIX] قيم الخزينة قبل الإصلاح:');
    console.log(`- الرصيد الافتتاحي: ${cashbox.initial_balance}`);
    console.log(`- الرصيد الحالي: ${cashbox.current_balance}`);
    console.log(`- إجمالي الأرباح: ${cashbox.profit_total}`);
    console.log(`- إجمالي المبيعات: ${cashbox.sales_total}`);
    console.log(`- إجمالي المشتريات: ${cashbox.purchases_total}`);
    console.log(`- إجمالي المرتجعات: ${cashbox.returns_total || 0}`);

    // إصلاح قيم الخزينة السالبة
    const fixedCashbox = cashboxUtils.fixNegativeCashboxValues(cashbox);

    // عرض قيم الخزينة بعد الإصلاح
    console.log('[CASHBOX-FIX] قيم الخزينة بعد الإصلاح:');
    console.log(`- الرصيد الافتتاحي: ${fixedCashbox.initial_balance}`);
    console.log(`- الرصيد الحالي: ${fixedCashbox.current_balance}`);
    console.log(`- إجمالي الأرباح: ${fixedCashbox.profit_total}`);
    console.log(`- إجمالي المبيعات: ${fixedCashbox.sales_total}`);
    console.log(`- إجمالي المشتريات: ${fixedCashbox.purchases_total}`);
    console.log(`- إجمالي المرتجعات: ${fixedCashbox.returns_total || 0}`);

    // تحديث الخزينة في قاعدة البيانات
    const updateStmt = db.prepare(`
      UPDATE cashbox
      SET current_balance = ?,
          profit_total = ?,
          sales_total = ?,
          purchases_total = ?,
          returns_total = ?,
          updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `);

    const updateResult = updateStmt.run(
      fixedCashbox.current_balance,
      fixedCashbox.profit_total,
      fixedCashbox.sales_total,
      fixedCashbox.purchases_total,
      fixedCashbox.returns_total || 0,
      fixedCashbox.id
    );

    console.log(`[CASHBOX-FIX] تم تحديث الخزينة بنجاح. عدد الصفوف المتأثرة: ${updateResult.changes}`);
    logSystem(`[CASHBOX-FIX] تم تحديث الخزينة بنجاح. عدد الصفوف المتأثرة: ${updateResult.changes}`, 'info');

    // إرسال إشعار بتحديث الخزينة
    eventSystem.notifyCashboxUpdated({
      id: fixedCashbox.id,
      current_balance: fixedCashbox.current_balance,
      sales_total: fixedCashbox.sales_total,
      purchases_total: fixedCashbox.purchases_total,
      returns_total: fixedCashbox.returns_total || 0,
      profit_total: fixedCashbox.profit_total,
      transaction_type: 'fix',
      success: true
    });

    return {
      success: true,
      message: 'تم إصلاح قيم الخزينة السالبة بنجاح',
      before: {
        current_balance: cashbox.current_balance,
        profit_total: cashbox.profit_total,
        sales_total: cashbox.sales_total,
        purchases_total: cashbox.purchases_total,
        returns_total: cashbox.returns_total || 0
      },
      after: {
        current_balance: fixedCashbox.current_balance,
        profit_total: fixedCashbox.profit_total,
        sales_total: fixedCashbox.sales_total,
        purchases_total: fixedCashbox.purchases_total,
        returns_total: fixedCashbox.returns_total || 0
      }
    };
  } catch (error) {
    console.error('[CASHBOX-FIX] خطأ في إصلاح قيم الخزينة السالبة:', error);
    logError(error, 'fixNegativeCashboxValues');
    return { success: false, error: error.message };
  }
}

// تصدير الوظائف
module.exports = {
  initialize,
  createTransaction,
  updateInventoryAfterTransaction,
  updateCashboxAfterTransaction,
  generateTransactionId,
  getTransactions,
  fixNegativeCashboxValues,
  recalculateTotalProfits,
  updateProfitTotalInDatabase
};
